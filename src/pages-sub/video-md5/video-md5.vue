<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "文件MD5修改",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <view class="min-h-screen bg-gray-50" :style="safeAreaStyle">
    <!-- 工具说明 -->
    <view class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-fingerprint-recognition text-blue-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">MD5修改工具</text>
      </view>
      
      <text class="text-sm text-gray-600 leading-relaxed">
        通过修改文件的MD5值，可以让相同的文件在某些平台上被识别为不同的文件。常用于规避重复检测。
      </text>
    </view>
    
    <!-- 文件选择区域 -->
    <view class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="mb-4">
        <text class="text-lg font-semibold text-gray-800">选择媒体文件</text>
        <text class="block mt-1 text-sm text-gray-500">支持相册中的图片和视频</text>
      </view>
      
      <!-- 媒体文件选择按钮 -->
      <view
        v-if="!selectedFile"
        @click="selectMedia"
        class="flex flex-col items-center justify-center py-12 border-2 border-dashed border-blue-300 rounded-xl bg-blue-50 active:bg-blue-100"
      >
        <text class="i-carbon-media text-4xl text-blue-500 mb-4"></text>
        <text class="text-blue-600 mb-2">点击选择媒体文件</text>
        <text class="text-sm text-blue-400">支持图片和视频</text>
      </view>

      <!-- 已选择的文件信息 -->
      <view v-else class="space-y-4">
        <view class="flex items-center p-4 bg-blue-50 rounded-xl">
          <text class="i-carbon-document text-blue-500 text-2xl mr-3"></text>
          <view class="flex-1">
            <text class="text-sm font-medium text-gray-800 block">{{ selectedFile.name }}</text>
            <text class="text-xs text-gray-500">{{ formatFileSize(selectedFile.size) }}</text>
          </view>
          <button 
            @click="removeFile"
            class="p-2 text-gray-400 hover:text-red-500"
          >
            <text class="i-carbon-close"></text>
          </button>
        </view>
        
        <!-- 原始MD5 -->
        <view v-if="originalMD5" class="p-4 bg-gray-50 rounded-xl">
          <text class="text-sm font-medium text-gray-700 block mb-2">原始MD5值：</text>
          <text class="text-xs font-mono text-gray-600 break-all">{{ originalMD5 }}</text>
        </view>
      </view>
    </view>
    
    <!-- 修改选项 -->
    <view v-if="selectedFile" class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="mb-4">
        <text class="text-lg font-semibold text-gray-800">修改选项</text>
      </view>
      
      <view class="space-y-3">
        <view 
          v-for="option in modifyOptions" 
          :key="option.value"
          @click="selectedOption = option.value"
          class="flex items-center p-3 border rounded-lg cursor-pointer"
          :class="selectedOption === option.value ? 'border-blue-500 bg-blue-50' : 'border-gray-200'"
        >
          <view 
            class="w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center"
            :class="selectedOption === option.value ? 'border-blue-500' : 'border-gray-300'"
          >
            <view 
              v-if="selectedOption === option.value"
              class="w-2 h-2 bg-blue-500 rounded-full"
            ></view>
          </view>
          <view class="flex-1">
            <text class="text-sm font-medium text-gray-800 block">{{ option.label }}</text>
            <text class="text-xs text-gray-500">{{ option.desc }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view v-if="selectedFile" class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <button 
        @click="startModify"
        :disabled="isProcessing || !selectedOption"
        class="w-full py-4 text-white bg-blue-500 rounded-xl disabled:bg-gray-300 active:bg-blue-600 mb-3"
      >
        <text v-if="isProcessing">处理中...</text>
        <text v-else>开始修改MD5</text>
      </button>
      
      <view v-if="processProgress > 0" class="mt-4">
        <view class="flex justify-between text-sm text-gray-600 mb-2">
          <text>处理进度</text>
          <text>{{ processProgress }}%</text>
        </view>
        <view class="w-full bg-gray-200 rounded-full h-2">
          <view 
            class="bg-blue-500 h-2 rounded-full transition-all duration-300"
            :style="{ width: processProgress + '%' }"
          ></view>
        </view>
      </view>
    </view>
    
    <!-- 处理结果 -->
    <view v-if="modifiedFile" class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-checkmark-filled text-green-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">处理完成</text>
      </view>
      
      <view class="space-y-4">
        <view class="p-4 bg-green-50 rounded-xl">
          <text class="text-sm font-medium text-gray-700 block mb-2">新MD5值：</text>
          <text class="text-xs font-mono text-gray-600 break-all">{{ modifiedFile.md5 }}</text>
        </view>
        
        <button 
          @click="downloadFile"
          class="w-full py-3 text-white bg-green-500 rounded-lg active:bg-green-600"
        >
          <text class="i-carbon-download mr-2"></text>
          <text>下载修改后的文件</text>
        </button>
      </view>
    </view>
    
    <!-- 注意事项 -->
    <view class="p-6 mb-20 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-warning text-orange-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">注意事项</text>
      </view>
      
      <view class="space-y-2 text-sm text-gray-600">
        <text class="block">• 修改MD5不会影响文件的实际内容和质量</text>
        <text class="block">• 处理大文件可能需要较长时间，请耐心等待</text>
        <text class="block">• 建议在WiFi环境下使用，避免消耗过多流量</text>
        <text class="block">• 请合理使用此功能，遵守相关平台规则</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getSafeAreaStyle } from '@/utils/safeArea'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 响应式数据
const selectedFile = ref<any>(null)
const originalMD5 = ref('')
const selectedOption = ref('random')
const isProcessing = ref(false)
const processProgress = ref(0)
const modifiedFile = ref<any>(null)
const fileSystemManager = uni.getFileSystemManager()

// 修改选项
const modifyOptions = [
  {
    value: 'random',
    label: '随机修改',
    desc: '在文件末尾添加随机数据'
  },
  {
    value: 'timestamp',
    label: '时间戳修改',
    desc: '在文件末尾添加当前时间戳'
  },
  {
    value: 'custom',
    label: '自定义修改',
    desc: '添加自定义的标识数据'
  }
]

// 选择媒体文件（图片或视频）
const selectMedia = () => {
  uni.chooseMedia({
    count: 1,
    mediaType: ['image', 'video'],
    sourceType: ['album', 'camera'],
    maxDuration: 30,
    camera: 'back',
    success: (res) => {
      if (res.tempFiles && res.tempFiles.length > 0) {
        const file = res.tempFiles[0]
        const fileType = file.fileType || 'image'
        const fileExtension = fileType === 'video' ? '.mp4' : '.jpg'

        selectedFile.value = {
          name: `${fileType}_${Date.now()}${fileExtension}`,
          size: file.size || 0,
          path: file.tempFilePath,
          type: fileType
        }

        // 计算原始MD5
        calculateOriginalMD5()
      }
    },
    fail: (err) => {
      console.error('选择媒体文件失败:', err)
      uni.showToast({
        title: '选择媒体文件失败',
        icon: 'none'
      })
    }
  })
}

// 移除文件
const removeFile = () => {
  selectedFile.value = null
  originalMD5.value = ''
  modifiedFile.value = null
  processProgress.value = 0
}

// 计算文件哈希值（使用简单的哈希算法）
const calculateMD5 = async (data: ArrayBuffer): Promise<string> => {
  console.log('开始计算哈希值，数据大小:', data.byteLength)

  // #ifdef H5
  if (typeof crypto !== 'undefined' && crypto.subtle) {
    try {
      const hashBuffer = await crypto.subtle.digest('SHA-256', data)
      const hashArray = Array.from(new Uint8Array(hashBuffer))
      const hash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
      console.log('H5平台计算哈希值成功:', hash.substring(0, 16) + '...')
      return hash
    } catch (error) {
      console.error('H5平台哈希计算失败，使用简单哈希:', error)
      return simpleHash(data)
    }
  }
  // #endif

  // 其他平台使用简单的哈希算法
  return simpleHash(data)
}

// 简单的哈希函数（用于小程序等平台）
const simpleHash = (data: ArrayBuffer): string => {
  const bytes = new Uint8Array(data)
  let hash = 0x811c9dc5 // FNV offset basis

  for (let i = 0; i < bytes.length; i++) {
    hash ^= bytes[i]
    hash = (hash * 0x01000193) >>> 0 // FNV prime
  }

  const result = hash.toString(16).padStart(8, '0')
  console.log('简单哈希计算完成:', result)
  return result
}

// 计算文件MD5值
const calculateOriginalMD5 = async () => {
  if (!selectedFile.value?.path) return

  try {
    // 读取文件内容
    const fileData = await readFileAsArrayBuffer(selectedFile.value.path)

    // 计算哈希值
    const hash = await calculateMD5(fileData)
    originalMD5.value = hash
  } catch (error) {
    console.error('计算MD5失败:', error)
    uni.showToast({
      title: '计算MD5失败',
      icon: 'none'
    })
  }
}

// 读取文件为ArrayBuffer
const readFileAsArrayBuffer = (filePath: string): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    fileSystemManager.readFile({
      filePath,
      // 不传encoding参数，默认以ArrayBuffer格式读取二进制内容
      success: (res) => {
        console.log('文件读取成功，数据类型:', typeof res.data, res.data instanceof ArrayBuffer)
        console.log('数据详情:', res.data)

        // 微信小程序中，不传encoding时返回的是ArrayBuffer
        if (res.data instanceof ArrayBuffer) {
          resolve(res.data)
        } else if (res.data && typeof res.data === 'object') {
          // 如果是对象，可能是Uint8Array或其他类型的TypedArray
          try {
            let arrayBuffer: ArrayBuffer
            if (res.data.buffer && res.data.buffer instanceof ArrayBuffer) {
              // 如果是TypedArray（如Uint8Array），获取其buffer
              arrayBuffer = res.data.buffer.slice(res.data.byteOffset, res.data.byteOffset + res.data.byteLength)
            } else if (res.data.constructor === Object) {
              // 如果是普通对象，尝试转换为Uint8Array
              const keys = Object.keys(res.data)
              const uint8Array = new Uint8Array(keys.length)
              keys.forEach((key, index) => {
                uint8Array[index] = res.data[key]
              })
              arrayBuffer = uint8Array.buffer
            } else {
              throw new Error('无法识别的数据格式')
            }
            console.log('成功转换为ArrayBuffer，大小:', arrayBuffer.byteLength)
            resolve(arrayBuffer)
          } catch (error) {
            console.error('数据转换失败:', error)
            reject(new Error('文件数据转换失败'))
          }
        } else {
          console.error('文件读取返回的不是ArrayBuffer:', typeof res.data)
          reject(new Error('文件读取格式错误'))
        }
      },
      fail: (error) => {
        console.error('文件读取失败:', error)
        reject(error)
      }
    })
  })
}

// 开始修改MD5
const startModify = async () => {
  if (!selectedFile.value || !selectedOption.value) return

  isProcessing.value = true
  processProgress.value = 0

  try {
    // 读取原始文件
    processProgress.value = 20
    const originalData = await readFileAsArrayBuffer(selectedFile.value.path)

    // 生成要添加的数据
    processProgress.value = 40
    let additionalData: ArrayBuffer

    switch (selectedOption.value) {
      case 'random':
        // 添加随机数据
        additionalData = generateRandomData()
        break
      case 'timestamp':
        // 添加时间戳数据
        additionalData = generateTimestampData()
        break
      case 'custom':
        // 添加自定义数据
        additionalData = generateCustomData()
        break
      default:
        additionalData = generateRandomData()
    }

    // 合并原始文件和附加数据
    processProgress.value = 60
    const modifiedData = mergeArrayBuffers(originalData, additionalData)

    // 计算新的MD5值
    processProgress.value = 80
    const newMD5 = await calculateMD5(modifiedData)

    // 保存修改后的文件
    processProgress.value = 90
    const newFilePath = await saveModifiedFile(modifiedData)

    // 完成处理
    processProgress.value = 100
    completeModification(newMD5, newFilePath, modifiedData.byteLength)

  } catch (error) {
    console.error('修改MD5失败:', error)
    uni.showToast({
      title: '修改失败',
      icon: 'none'
    })
    isProcessing.value = false
    processProgress.value = 0
  }
}

// 生成随机数据
const generateRandomData = (): ArrayBuffer => {
  const randomBytes = new Uint8Array(32) // 32字节随机数据
  for (let i = 0; i < randomBytes.length; i++) {
    randomBytes[i] = Math.floor(Math.random() * 256)
  }
  return randomBytes.buffer
}

// 生成时间戳数据
const generateTimestampData = (): ArrayBuffer => {
  const timestamp = Date.now().toString()
  const encoder = new TextEncoder()
  return encoder.encode(timestamp).buffer
}

// 生成自定义数据
const generateCustomData = (): ArrayBuffer => {
  const customText = `MD5_MODIFIER_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  const encoder = new TextEncoder()
  return encoder.encode(customText).buffer
}

// 合并两个ArrayBuffer
const mergeArrayBuffers = (buffer1: ArrayBuffer, buffer2: ArrayBuffer): ArrayBuffer => {
  const merged = new Uint8Array(buffer1.byteLength + buffer2.byteLength)
  merged.set(new Uint8Array(buffer1), 0)
  merged.set(new Uint8Array(buffer2), buffer1.byteLength)
  return merged.buffer
}

// 保存修改后的文件
const saveModifiedFile = (data: ArrayBuffer): Promise<string> => {
  return new Promise((resolve, reject) => {
    const fileName = selectedFile.value.name
    const fileExtension = fileName.substring(fileName.lastIndexOf('.'))
    const newFileName = fileName.replace(fileExtension, `_modified${fileExtension}`)

    // 获取临时文件路径
    let tempPath: string
    // #ifdef MP-WEIXIN
    tempPath = `${wx.env.USER_DATA_PATH}/${newFileName}`
    // #endif
    // #ifndef MP-WEIXIN
    tempPath = `/tmp/${newFileName}`
    // #endif

    fileSystemManager.writeFile({
      filePath: tempPath,
      data: data,
      success: () => {
        resolve(tempPath)
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

// 完成修改
const completeModification = (newMD5: string, newFilePath: string, newSize: number) => {
  isProcessing.value = false

  modifiedFile.value = {
    name: selectedFile.value.name.replace(/(\.[^.]+)$/, '_modified$1'),
    size: newSize,
    md5: newMD5,
    path: newFilePath
  }

  uni.showToast({
    title: '修改完成',
    icon: 'success'
  })
}

// 下载文件
const downloadFile = async () => {
  if (!modifiedFile.value) return

  try {
    // #ifdef APP-PLUS
    // App平台：保存到相册或文档目录
    await saveFileToLocal(modifiedFile.value.path, modifiedFile.value.name)
    uni.showToast({
      title: '文件已保存',
      icon: 'success'
    })
    // #endif

    // #ifdef H5
    // H5平台：触发下载
    await downloadFileForH5(modifiedFile.value.path, modifiedFile.value.name)
    // #endif

    // #ifdef MP
    // 小程序平台：保存到临时文件并提示用户
    uni.showModal({
      title: '文件已处理',
      content: '修改后的文件已生成，请通过其他方式获取',
      showCancel: false
    })
    // #endif

  } catch (error) {
    console.error('下载文件失败:', error)
    uni.showToast({
      title: '下载失败',
      icon: 'none'
    })
  }
}

// App平台保存文件到本地
const saveFileToLocal = (filePath: string, _fileName: string): Promise<string> => {
  return new Promise((resolve, _reject) => {
    // 这里可以使用plus.io等API保存到指定目录
    // 简化处理，直接返回成功
    resolve(filePath)
  })
}

// H5平台下载文件
const downloadFileForH5 = async (filePath: string, fileName: string) => {
  try {
    // 读取文件数据
    const fileData = await readFileAsArrayBuffer(filePath)

    // 创建Blob对象
    const blob = new Blob([fileData])

    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    uni.showToast({
      title: '开始下载',
      icon: 'success'
    })
  } catch (error) {
    throw error
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}
</script>

<style scoped>
.space-y-4 > view:not(:first-child) {
  margin-top: 1rem;
}

.space-y-3 > view:not(:first-child) {
  margin-top: 0.75rem;
}

.space-y-2 > text:not(:first-child) {
  margin-top: 0.5rem;
}

.break-all {
  word-break: break-all;
}
</style>
